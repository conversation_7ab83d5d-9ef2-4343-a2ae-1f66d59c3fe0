import React, { useState, useEffect } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { ChevronDown, Menu, X, User, LogOut } from 'lucide-react'
import { authService } from '../services/authService'

export default function Header() {
  const [findCareOpen, setFindCareOpen] = useState(false)
  const [careGroupsOpen, setCareGroupsOpen] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [user, setUser] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const location = useLocation()
  const navigate = useNavigate()

  // Close all dropdowns when route changes for proper Apple Mac desktop UX
  useEffect(() => {
    setFindCareOpen(false)
    setCareGroupsOpen(false)
    setMobileMenuOpen(false)
    setUserMenuOpen(false)
  }, [location.pathname])

  // Handle search functionality
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Navigate to caregivers page with search query
      navigate(`/caregivers?search=${encodeURIComponent(searchQuery.trim())}`)
      setSearchQuery('')
    }
  }

  // Check authentication state
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser()
        setUser(currentUser)
      } catch (error) {
        setUser(null)
      }
    }
    checkAuth()
  }, [])

  const handleSignOut = async () => {
    try {
      await authService.signOut()
      setUser(null)
      setUserMenuOpen(false)
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  return (
    <>
      {/* Skip Links for Accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      <a href="#navigation" className="skip-link">
        Skip to navigation
      </a>

      <header
        role="banner"
        className="w-full px-4 py-3 sm:px-6 sm:py-4 md:px-8 md:py-4 lg:px-10 lg:py-5 flex items-center relative backdrop-blur-md"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderBottom: '1px solid var(--border-separator)',
          boxShadow: 'var(--shadow-light)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)'
        }}
      >
      {/* Enhanced Logo with Heart Icon */}
      <Link to="/" className="flex items-center no-underline gap-3 group">
        <div
          className="w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200 group-hover:scale-105"
          style={{ backgroundColor: 'var(--primary)' }}
        >
          <svg className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
          </svg>
        </div>
        <div className="flex flex-col">
          <span
            className="text-lg macos-title transition-all duration-200 group-hover:text-opacity-80"
            style={{ color: 'var(--text-primary)' }}
          >
            Care Connector
          </span>
          <span
            className="text-xs macos-caption"
            style={{ color: 'var(--text-secondary)' }}
          >
            Healthcare Coordination
          </span>
        </div>
      </Link>

      {/* Enhanced Mobile Menu Button */}
      <div className="flex items-center md:hidden ml-auto">
        <button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="bg-transparent border-none p-3 rounded-xl transition-all duration-200"
          aria-label="Toggle mobile menu"
          style={{ color: 'var(--text-secondary)' }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
            e.currentTarget.style.color = 'var(--text-primary)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
            e.currentTarget.style.color = 'var(--text-secondary)'
          }}
        >
          {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Centered Navigation Container */}
      <div className="flex-1 flex justify-center">
        {/* Desktop Navigation */}
        <nav
          id="navigation"
          role="navigation"
          aria-label="Main navigation"
          className="hidden md:flex items-center gap-6"
        >
        {/* Find Care Dropdown */}
        <div className="relative group">
          <button
            onClick={() => setFindCareOpen(!findCareOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                setFindCareOpen(!findCareOpen)
              } else if (e.key === 'Escape' && findCareOpen) {
                setFindCareOpen(false)
              }
            }}
            aria-expanded={findCareOpen}
            aria-haspopup="true"
            aria-controls="find-care-menu"
            aria-label="Find Care Services"
            className="flex items-center gap-2.5 px-4 py-2 rounded-lg transition-all duration-150 ease-out font-medium focus:outline-none focus:ring-2 focus:ring-offset-2"
            style={{
              // Focus ring styles handled by Tailwind classes
            } as React.CSSProperties}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            <span className="font-medium" style={{ color: 'var(--text-primary)' }}>Find Care</span>
            <ChevronDown
              className={`w-4 h-4 transition-transform duration-200 ease-[cubic-bezier(0.23,1,0.32,1)] ${findCareOpen ? 'rotate-180' : ''}`}
              style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            />
          </button>
          {findCareOpen && (
          <div
            id="find-care-menu"
            role="menu"
            aria-orientation="vertical"
            className={`absolute top-full left-0 mt-2 rounded-xl py-2 w-48 sm:w-56 md:w-64 lg:min-w-[240px] z-[100] transform transition-all duration-300 ease-[cubic-bezier(0.23,1,0.32,1)] ${
              findCareOpen ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-95 -translate-y-2'
            }`}
            style={{
              backgroundColor: 'var(--bg-primary)',
              boxShadow: 'var(--shadow-dropdown)',
              border: '1px solid var(--border-light)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)'
            }}
          >
            <Link
              to="/caregivers"
              onClick={() => setFindCareOpen(false)}
              role="menuitem"
              tabIndex={findCareOpen ? 0 : -1}
              className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                location.pathname === '/caregivers' ? 'font-semibold' : ''
              }`}
              style={{
                color: location.pathname === '/caregivers' ? 'var(--primary)' : 'var(--text-secondary)'
              }}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  setFindCareOpen(false);
                }
              }}
            >
              Caregivers
            </Link>
            <div className="border-b my-1" style={{ borderColor: 'var(--border-light)' }} />
            <Link
              to="/companions"
              onClick={() => setFindCareOpen(false)}
              className="dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 font-medium"
              style={{
                color: location.pathname === '/companions' ? 'var(--primary)' : 'var(--text-secondary)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
            >
              Companions
            </Link>
            <div className="border-b my-1" style={{ borderColor: 'var(--border-light)' }} />
            <Link
              to="/professionals"
              onClick={() => setFindCareOpen(false)}
              className="dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 font-medium"
              style={{
                color: location.pathname === '/professionals' ? 'var(--primary)' : 'var(--text-secondary)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
              }}
            >
              Professionals
            </Link>
            <div className="border-b my-1" style={{ borderColor: 'var(--border-light)' }} />
            <Link
              to="/care-checkers"
              onClick={() => setFindCareOpen(false)}
              className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                location.pathname === '/care-checkers' ? 'font-semibold' : ''
              }`}
              style={{ 
                color: location.pathname === '/care-checkers' ? 'var(--primary)' : 'var(--text-secondary)'
              }}
            >
              Care Checkers
            </Link>
          </div>
          )}
        </div>

        {/* Care Groups Dropdown */}
        <div className="relative">
          <button
            onClick={() => setCareGroupsOpen(!careGroupsOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                setCareGroupsOpen(!careGroupsOpen)
              } else if (e.key === 'Escape' && careGroupsOpen) {
                setCareGroupsOpen(false)
              }
            }}
            aria-expanded={careGroupsOpen}
            aria-haspopup="true"
            aria-controls="care-groups-menu"
            className="flex items-center gap-2.5 px-4 py-2 rounded-lg transition-all duration-150 ease-out font-medium focus:outline-none focus:ring-2 focus:ring-offset-2"
            style={{ color: 'var(--text-primary)', opacity: 0.85 }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            Care Groups
            <ChevronDown className="w-4 h-4" />
          </button>
          {careGroupsOpen && (
            <div
              id="care-groups-menu"
              role="menu"
              aria-orientation="vertical"
              className="absolute top-full left-0 mt-1 rounded-xl py-1.5 min-w-[220px] z-[100]"
              style={{
                backgroundColor: 'var(--bg-primary)',
                boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)',
                border: '1px solid var(--border-light)'
              }}
            >
              <Link
                to="/browse-groups"
                onClick={() => setCareGroupsOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/browse-groups' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/browse-groups' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Browse Groups
              </Link>
              <div className="border-b border-gray-100/80 my-1" />
              <Link
                to="/create-group"
                onClick={() => setCareGroupsOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/create-group' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/create-group' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Create Group
              </Link>
              <div className="border-b border-gray-100/80 my-1" />
              <Link
                to="/join-group"
                onClick={() => setCareGroupsOpen(false)}
                className={`dropdown-link block py-2.5 px-4 no-underline text-sm transition-all duration-200 hover:bg-black/5 ${
                  location.pathname === '/join-group' ? 'font-semibold' : ''
                }`}
                style={{ 
                  color: location.pathname === '/join-group' ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                Join a Group
              </Link>
            </div>
          )}
        </div>

        </nav>

        {/* Search Bar */}
        <div className="hidden md:flex items-center ml-4 lg:ml-8">
          <form onSubmit={handleSearch} className="relative flex items-center">
            <input
              type="text"
              placeholder="Search by location, specialty, or name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-80 px-4 py-2 pl-10 pr-12 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1"
              style={{
                backgroundColor: 'var(--bg-secondary)',
                borderColor: 'var(--border-light)',
                color: 'var(--text-primary)'
              } as React.CSSProperties}
            />
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
              style={{ color: 'var(--text-muted)' }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-lg btn-primary"
              title="Search"
              aria-label="Search for care providers"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </form>
        </div>
      </div>

      {/* Enhanced Right Side Navigation - Conditional Auth/User Menu */}
      <div className="flex items-center gap-3">
        {user ? (
          /* User Menu when logged in */
          <div className="relative">
            <button
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 hover:shadow-md"
              style={{
                backgroundColor: userMenuOpen ? 'var(--bg-secondary)' : 'transparent',
                color: 'var(--text-primary)'
              }}
              aria-label="User menu"
              aria-expanded={userMenuOpen}
            >
              <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--accent)' }}>
                <User className="w-4 h-4" style={{ color: 'var(--text-primary)' }} />
              </div>
              <span className="text-sm font-medium hidden sm:block">
                {user.first_name || user.email?.split('@')[0] || 'User'}
              </span>
              <ChevronDown className={`w-4 h-4 transition-transform ${userMenuOpen ? 'rotate-180' : ''}`} />
            </button>

            {userMenuOpen && (
              <div
                className="absolute right-0 top-full mt-2 w-48 sm:w-56 md:w-64 lg:w-72 rounded-2xl shadow-2xl z-50 border"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)'
                }}
              >
                <div className="p-4 border-b" style={{ borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--accent)' }}>
                      <User className="w-5 h-5" style={{ color: 'var(--text-primary)' }} />
                    </div>
                    <div>
                      <div className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                        {user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : user.full_name || 'User'}
                      </div>
                      <div className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                        {user.email}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="py-2">
                  <Link
                    to="/dashboard"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-gray-50"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    <User className="w-4 h-4" />
                    <span className="text-sm">Dashboard</span>
                  </Link>
                  <Link
                    to="/profile"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-gray-50"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    <User className="w-4 h-4" />
                    <span className="text-sm">Profile</span>
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-gray-50 text-left"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    <LogOut className="w-4 h-4" />
                    <span className="text-sm">Sign Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Auth buttons when not logged in */
          <>
            <Link
              to="/sign-in"
              className="no-underline text-sm font-semibold transition-all duration-200 px-4 py-2 rounded-xl"
              style={{ color: 'var(--text-secondary)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                e.currentTarget.style.color = 'var(--text-primary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = 'var(--text-secondary)'
              }}
            >
              Sign In
            </Link>
            <Link
              to="/sign-up"
              className="no-underline text-sm font-semibold px-5 py-2.5 rounded-xl border-2 transition-all duration-200 hover:shadow-md hover:scale-105"
              style={{
                color: 'var(--primary)',
                borderColor: 'var(--primary)',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--primary)'
                e.currentTarget.style.color = 'var(--bg-primary)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = 'var(--primary)'
              }}
            >
              Sign Up
            </Link>
          </>
        )}
      </div>

      {/* Enhanced Mobile Menu Overlay - Apple iOS Style */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 z-50">
          <div
            className="p-6 space-y-6 rounded-b-2xl"
            style={{
              backgroundColor: 'var(--bg-primary)',
              boxShadow: 'var(--shadow-dropdown)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              borderTop: '1px solid var(--border-light)'
            }}
          >
            {/* Mobile Find Care Section */}
            <div role="group" aria-labelledby="mobile-find-care-heading">
              <div id="mobile-find-care-heading" className="font-medium text-sm mb-2" style={{ color: 'var(--text-primary)' }}>Find Care</div>
              <div className="pl-4 space-y-2" role="list">
                <Link
                  to="/caregivers"
                  onClick={() => setMobileMenuOpen(false)}
                  role="listitem"
                  className="block px-4 py-2 rounded-lg transition-all duration-200 font-medium"
                  style={{
                    color: 'var(--text-primary)',
                    opacity: 0.85
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  Caregivers
                </Link>
                <Link
                  to="/companions"
                  onClick={() => setMobileMenuOpen(false)}
                  role="listitem"
                  className="block px-4 py-2 rounded-lg transition-all duration-200 font-medium"
                  style={{
                    color: 'var(--text-primary)',
                    opacity: 0.85
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  Companions
                </Link>
                <Link
                  to="/professionals"
                  onClick={() => setMobileMenuOpen(false)}
                  className="block px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Professionals
                </Link>
                <Link
                  to="/care-checkers"
                  onClick={() => setMobileMenuOpen(false)}
                  className="block px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Care Checkers
                </Link>
              </div>
            </div>

            {/* Mobile Care Groups Section */}
            <div>
              <div className="font-medium text-sm mb-2" style={{ color: 'var(--text-primary)' }}>Care Groups</div>
              <div className="pl-4 space-y-2">
                <Link
                  to="/browse-groups"
                  onClick={() => setMobileMenuOpen(false)}
                  className="block px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Browse Groups
                </Link>
                <Link
                  to="/create-group"
                  onClick={() => setMobileMenuOpen(false)}
                  className="block px-4 py-2 rounded-lg transition-all duration-200 font-medium"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Create Group
                </Link>
                <Link
                  to="/join-group"
                  onClick={() => setMobileMenuOpen(false)}
                  className="block px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                  style={{ color: 'var(--text-primary)', opacity: 0.85 }}
                >
                  Join a Group
                </Link>
              </div>
            </div>

            {/* Mobile Other Links */}
            <div className="space-y-2">
              <Link
                to="/how-it-works"
                onClick={() => setMobileMenuOpen(false)}
                className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                style={{ color: 'var(--text-primary)', opacity: 0.85 }}
              >
                How It Works
              </Link>
              <Link
                to="/features"
                onClick={() => setMobileMenuOpen(false)}
                className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                style={{ color: 'var(--text-primary)', opacity: 0.85 }}
              >
                Features
              </Link>
              <Link
                to="/products"
                onClick={() => setMobileMenuOpen(false)}
                className="px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 font-medium"
                style={{ color: 'var(--text-primary)', opacity: 0.85 }}
              >
                Products
              </Link>
            </div>

            {/* Mobile Auth/User Section */}
            <div className="pt-4 border-t space-y-3" style={{ borderColor: 'var(--border-light)' }}>
              {user ? (
                /* Mobile User Menu when logged in */
                <>
                  <div className="flex items-center gap-3 px-4 py-3 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                    <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--accent)' }}>
                      <User className="w-4 h-4" style={{ color: 'var(--text-primary)' }} />
                    </div>
                    <div>
                      <div className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>
                        {user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : user.full_name || 'User'}
                      </div>
                      <div className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                        {user.email}
                      </div>
                    </div>
                  </div>
                  <Link
                    to="/dashboard"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    <User className="w-4 h-4" />
                    <span className="text-sm font-medium">Dashboard</span>
                  </Link>
                  <Link
                    to="/profile"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    <User className="w-4 h-4" />
                    <span className="text-sm font-medium">Profile</span>
                  </Link>
                  <button
                    onClick={() => {
                      handleSignOut()
                      setMobileMenuOpen(false)
                    }}
                    className="w-full flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-200 hover:bg-gray-100 active:bg-gray-200 text-left"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    <LogOut className="w-4 h-4" />
                    <span className="text-sm font-medium">Sign Out</span>
                  </button>
                </>
              ) : (
                /* Mobile Auth buttons when not logged in */
                <>
                  <Link
                    to="/sign-in"
                    onClick={() => setMobileMenuOpen(false)}
                    className="block py-2 text-sm font-medium"
                    style={{ color: 'var(--text-secondary)' }}
                  >
                    Sign In
                  </Link>
                  <Link
                    to="/sign-up"
                    onClick={() => setMobileMenuOpen(false)}
                    className="block py-2 text-sm font-medium"
                    style={{ color: 'var(--text-secondary)' }}
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
    </>
  )
}
