import React, { useState } from 'react'
import { CheckSquare, Plus, Clock, User, Flag, Calendar, Filter, X } from 'lucide-react'

export default function TaskManagement() {
  const [activeTab, setActiveTab] = useState('all')
  const [showCompleted, setShowCompleted] = useState(false)
  const [showNewTaskModal, setShowNewTaskModal] = useState(false)
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    dueTime: '',
    assignee: '',
    priority: 'medium',
    category: 'medication',
    recurring: false
  })

  const tasks = [
    {
      id: 1,
      title: "Give morning medication",
      description: "Blood pressure medication (Lisinopril 10mg)",
      dueTime: "8:00 AM",
      assignee: "Primary Caregiver",
      priority: "high",
      category: "medication",
      completed: false,
      recurring: true
    },
    {
      id: 2,
      title: "Physical therapy exercises",
      description: "15-minute leg strengthening routine",
      dueTime: "10:30 AM",
      assignee: "Physical Therapist",
      priority: "medium",
      category: "therapy",
      completed: true,
      recurring: true
    },
    {
      id: 3,
      title: "Check blood pressure",
      description: "Record morning blood pressure reading",
      dueTime: "9:00 AM",
      assignee: "You",
      priority: "high",
      category: "monitoring",
      completed: false,
      recurring: true
    },
    {
      id: 4,
      title: "Grocery shopping",
      description: "Pick up prescriptions and healthy snacks",
      dueTime: "2:00 PM",
      assignee: "Family Member",
      priority: "low",
      category: "errands",
      completed: false,
      recurring: false
    },
    {
      id: 5,
      title: "Evening medication",
      description: "Diabetes medication after dinner",
      dueTime: "7:00 PM",
      assignee: "Primary Caregiver",
      priority: "high",
      category: "medication",
      completed: false,
      recurring: true
    }
  ]

  const categories = [
    { id: 'all', name: 'All Tasks', count: tasks.length },
    { id: 'medication', name: 'Medication', count: tasks.filter(t => t.category === 'medication').length },
    { id: 'therapy', name: 'Therapy', count: tasks.filter(t => t.category === 'therapy').length },
    { id: 'monitoring', name: 'Monitoring', count: tasks.filter(t => t.category === 'monitoring').length },
    { id: 'errands', name: 'Errands', count: tasks.filter(t => t.category === 'errands').length }
  ]

  const filteredTasks = tasks.filter(task => {
    if (activeTab !== 'all' && task.category !== activeTab) return false
    if (!showCompleted && task.completed) return false
    return true
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700'
      case 'medium': return 'bg-yellow-100 text-yellow-700'
      case 'low': return 'bg-green-100 text-green-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'medication': return '💊'
      case 'therapy': return '🏃‍♀️'
      case 'monitoring': return '📊'
      case 'errands': return '🛒'
      default: return '📋'
    }
  }

  const handleCreateTask = async () => {
    // Add new task to the tasks array (in production, this would be a database call)
    const newTaskWithId = {
      ...newTask,
      id: tasks.length + 1,
      completed: false
    }
    
    // Reset form and close modal
    setNewTask({
      title: '',
      description: '',
      dueTime: '',
      assignee: '',
      priority: 'medium',
      category: 'medication',
      recurring: false
    })
    setShowNewTaskModal(false)
    
    // In production, would call Supabase to create task
    console.log('Creating new task:', newTaskWithId)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Task Management</h1>
              <p className="mt-2 text-gray-600">Organize and track daily care activities</p>
            </div>
            <button 
              onClick={() => setShowNewTaskModal(true)}
              className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-logo-green-dark flex items-center transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Task
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveTab(category.id)}
                    className={`w-full text-left px-3 py-2 rounded-lg flex items-center justify-between ${
                      activeTab === category.id 
                        ? 'bg-teal-100 text-teal-700' 
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    <span>{category.name}</span>
                    <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Show Completed</span>
                  <button
                    onClick={() => setShowCompleted(!showCompleted)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      showCompleted ? 'bg-teal-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        showCompleted ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-xl shadow-lg p-6 mt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Progress</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Completed</span>
                  <span className="font-medium text-green-600">
                    {tasks.filter(t => t.completed).length}/{tasks.length}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${(tasks.filter(t => t.completed).length / tasks.length) * 100}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500">
                  {tasks.filter(t => !t.completed && t.priority === 'high').length} high priority remaining
                </div>
              </div>
            </div>
          </div>

          {/* Task List */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-lg">
              {/* Filter Bar */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {activeTab === 'all' ? 'All Tasks' : categories.find(c => c.id === activeTab)?.name}
                  </h2>
                  <div className="flex items-center space-x-3">
                    <button className="button-secondary flex items-center text-sm transition-colors hover:opacity-90">
                      <Filter className="w-4 h-4 mr-1" />
                      Filter
                    </button>
                    <button className="button-secondary flex items-center text-sm transition-colors hover:opacity-90">
                      <Calendar className="w-4 h-4 mr-1" />
                      Schedule
                    </button>
                  </div>
                </div>
              </div>

              {/* Task Items */}
              <div className="divide-y divide-gray-200">
                {filteredTasks.map((task) => (
                  <div key={task.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-start space-x-4">
                      <button
                        className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center ${
                          task.completed 
                            ? 'bg-green-600 border-green-600 text-white' 
                            : 'border-gray-300 hover:border-green-500'
                        }`}
                      >
                        {task.completed && <CheckSquare className="w-3 h-3" />}
                      </button>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className={`text-lg font-medium ${
                              task.completed ? 'text-gray-500 line-through' : 'text-gray-900'
                            }`}>
                              {task.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                            
                            <div className="flex items-center space-x-4 mt-3">
                              <div className="flex items-center text-sm text-gray-500">
                                <Clock className="w-4 h-4 mr-1" />
                                {task.dueTime}
                              </div>
                              <div className="flex items-center text-sm text-gray-500">
                                <User className="w-4 h-4 mr-1" />
                                {task.assignee}
                              </div>
                              {task.recurring && (
                                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                  Recurring
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(task.priority)}`}>
                              <Flag className="w-3 h-3 inline mr-1" />
                              {task.priority}
                            </span>
                            <span className="text-lg">
                              {getCategoryIcon(task.category)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredTasks.length === 0 && (
                <div className="p-12 text-center">
                  <CheckSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
                  <p className="text-gray-600">
                    {showCompleted 
                      ? "No completed tasks in this category." 
                      : "All tasks in this category are complete!"}
                  </p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="mt-6 bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button className="button-secondary p-4 rounded-lg text-center transition-colors hover:opacity-90">
                  <Plus className="w-5 h-5 mx-auto mb-2" style={{ color: 'var(--primary)' }} />
                  <span className="text-sm font-medium">Add Medication Reminder</span>
                </button>
                <button className="button-secondary p-4 rounded-lg text-center transition-colors hover:opacity-90">
                  <Calendar className="w-5 h-5 mx-auto mb-2" style={{ color: 'var(--primary)' }} />
                  <span className="text-sm font-medium">Schedule Therapy</span>
                </button>
                <button className="button-secondary p-4 rounded-lg text-center transition-colors hover:opacity-90">
                  <CheckSquare className="w-5 h-5 mx-auto mb-2" style={{ color: 'var(--primary)' }} />
                  <span className="text-sm font-medium">Create Care Plan</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* New Task Modal */}
        {showNewTaskModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Create New Task</h2>
                <button
                  onClick={() => setShowNewTaskModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Task Title *
                  </label>
                  <input
                    type="text"
                    value={newTask.title}
                    onChange={(e) => setNewTask({...newTask, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-logo-green"
                    placeholder="Enter task title"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={newTask.description}
                    onChange={(e) => setNewTask({...newTask, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-logo-green h-20 resize-none"
                    placeholder="Enter task description"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Time
                    </label>
                    <input
                      type="time"
                      value={newTask.dueTime}
                      onChange={(e) => setNewTask({...newTask, dueTime: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-logo-green"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Assignee
                    </label>
                    <input
                      type="text"
                      value={newTask.assignee}
                      onChange={(e) => setNewTask({...newTask, assignee: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-logo-green"
                      placeholder="Assignee"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority
                    </label>
                    <select
                      value={newTask.priority}
                      onChange={(e) => setNewTask({...newTask, priority: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-logo-green"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      value={newTask.category}
                      onChange={(e) => setNewTask({...newTask, category: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-logo-green focus:border-logo-green"
                    >
                      <option value="medication">Medication</option>
                      <option value="therapy">Therapy</option>
                      <option value="monitoring">Monitoring</option>
                      <option value="errands">Errands</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="recurring"
                    checked={newTask.recurring}
                    onChange={(e) => setNewTask({...newTask, recurring: e.target.checked})}
                    className="h-4 w-4 text-logo-green focus:ring-logo-green border-gray-300 rounded"
                  />
                  <label htmlFor="recurring" className="ml-2 block text-sm text-gray-700">
                    Recurring task
                  </label>
                </div>
              </div>
              
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowNewTaskModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateTask}
                  disabled={!newTask.title.trim()}
                  className="flex-1 px-4 py-2 bg-logo-green text-white rounded-lg hover:bg-logo-green-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Create Task
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
