import { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'
import { ArrowLeft, Star, MapPin, Clock, MessageCircle, CheckCircle } from 'lucide-react'

interface Provider {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  rating?: number
  reviews_count?: number
  availability?: string[]
}

export default function ProviderProfile() {
  const { providerId, providerType } = useParams()
  const navigate = useNavigate()
  const [provider, setProvider] = useState<Provider | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [bookingStep, setBookingStep] = useState(1)
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([])
  const [paymentMethod, setPaymentMethod] = useState('')
  const [bookingError, setBookingError] = useState<string | null>(null)
  const [isBooking, setIsBooking] = useState(false)

  // Generate dynamic time slots based on business hours
  const generateTimeSlots = () => {
    const slots = []
    // Business hours: 9 AM to 6 PM, excluding lunch hour (12-1 PM)
    for (let hour = 9; hour <= 17; hour++) {
      if (hour !== 12) { // Skip lunch hour
        const time12 = hour > 12 ? `${hour - 12}:00 PM` : `${hour}:00 AM`
        slots.push(time12)
      }
    }
    return slots
  }


  // Memoize fetchProvider to prevent infinite loops
  const fetchProvider = useCallback(async () => {
    if (!providerId || !providerType) {
      console.log('Missing providerId or providerType:', { providerId, providerType })
      setError('Invalid provider information')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)
    
    try {
      console.log('=== ENHANCED FETCHPROVIDER DEBUG ===')
      console.log('Provider ID from URL:', providerId)
      console.log('Provider Type from URL:', providerType)
      console.log('Current provider state before fetch:', provider)
      
      let data: any[] = []
      
      console.log('Fetching provider:', { providerId, providerType })
      
      // Use real database connections for all provider types
      // Handle both singular (from URLs) and plural forms for compatibility
      switch (providerType) {
        case 'caregiver':
        case 'caregivers':
          data = await dataService.getCaregivers()
          break
        case 'companion':
        case 'companions':
          data = await dataService.getCompanions()
          break
        case 'professional':
        case 'professionals':
          data = await dataService.getProfessionals()
          break
        case 'care-checker':
        case 'care-checkers':
          data = await dataService.getCareCheckers()
          break
        default:
          throw new Error('Invalid provider type')
      }
      
      console.log('Available providers:', data.map(p => ({ id: p.id, name: p.name, idType: typeof p.id })))
      console.log('Looking for providerId:', providerId, 'Type:', typeof providerId)
      
      // Enhanced ID matching with type conversion and string comparison
      const foundProvider = data.find(p => {
        const match = p.id === providerId || String(p.id) === String(providerId)
        if (match) console.log('MATCH FOUND:', p.name, 'ID:', p.id)
        return match
      })
      
      if (!foundProvider) {
        console.error('Provider not found. Available IDs:', data.map(p => ({ id: p.id, type: typeof p.id })))
        console.error('Looking for ID:', providerId, 'Type:', typeof providerId)
        console.error('Exact comparison failed for all providers')
        throw new Error(`Provider with ID ${providerId} not found`)
      }
      
      console.log('Found provider:', foundProvider.name)
      console.log('Provider location:', foundProvider.location)
      console.log('Provider role:', foundProvider.provider_type)
      
      const processedProvider = {
        id: foundProvider.id,
        name: foundProvider.name || 'Unknown Provider', // Use the already processed name from dataService
        bio: foundProvider.bio || 'No bio available',
        location: foundProvider.location || 'Location not specified',
        specialties: foundProvider.specialties || [],
        verified: foundProvider.verified || false,
        hourly_rate: foundProvider.hourly_rate,
        years_experience: foundProvider.years_experience,
        rating: foundProvider.rating,
        reviews_count: foundProvider.reviews_count || 0,
        provider_type: foundProvider.provider_type,
      }
      
      console.log('Processed provider object:', processedProvider)
      console.log('About to call setProvider with:', processedProvider)
      setProvider(processedProvider)
      console.log('setProvider called - provider state should now be set')
      
      // Additional verification
      setTimeout(() => {
        console.log('Provider state after 100ms delay:', provider)
      }, 100)
      
    } catch (err) {
      console.error('fetchProvider error:', err)
      console.log('Setting error state:', err instanceof Error ? err.message : 'Failed to load provider')
      setError(err instanceof Error ? err.message : 'Failed to load provider')
    } finally {
      setLoading(false)
      console.log('Loading set to false')
      console.log('=== END ENHANCED FETCHPROVIDER DEBUG ===')
    }
  }, [providerId, providerType]) // Dependencies for useCallback

  useEffect(() => {
    fetchProvider()
  }, [fetchProvider]) // Now depends on memoized fetchProvider

  useEffect(() => {
    // Set available time slots when component mounts
    setAvailableTimeSlots(generateTimeSlots())
  }, [])

  const validateBookingDate = (date: string): string | null => {
    const today = new Date()
    const selectedDateObj = new Date(date)
    const todayStr = today.toISOString().split('T')[0]

    // Past date validation
    if (date < todayStr) {
      return 'Please select a future date for your appointment.'
    }

    // Maximum advance booking (90 days)
    const maxAdvanceDays = 90
    const maxDate = new Date()
    maxDate.setDate(today.getDate() + maxAdvanceDays)
    if (selectedDateObj > maxDate) {
      return `Bookings can only be made up to ${maxAdvanceDays} days in advance.`
    }

    // Weekend validation (assuming weekends are not available)
    const dayOfWeek = selectedDateObj.getDay()
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return 'Appointments are only available Monday through Friday.'
    }

    return null // No validation errors
  }

  const handleBookAppointment = () => {
    if (bookingStep === 1 && selectedDate) {
      // Comprehensive date validation
      const validationError = validateBookingDate(selectedDate)
      if (validationError) {
        setBookingError(validationError)
        return
      }
      // Generate available time slots for selected date
      setAvailableTimeSlots(generateTimeSlots())
      setBookingStep(2)
    } else if (bookingStep === 2 && selectedTime) {
      // Move to payment step
      setBookingStep(3)
    } else if (bookingStep === 3 && paymentMethod) {
      // Save booking to database with payment info
      saveBookingToDatabase()
    }
  }

  const saveBookingToDatabase = async () => {
    setIsBooking(true)
    setBookingError(null)

    try {
      // Calculate start and end times
      const [time, period] = selectedTime.split(' ')
      const [hours, minutes] = time.split(':')
      let hour24 = parseInt(hours)
      if (period === 'PM' && hour24 !== 12) hour24 += 12
      if (period === 'AM' && hour24 === 12) hour24 = 0

      const startDateTime = new Date(selectedDate)
      startDateTime.setHours(hour24, parseInt(minutes || '0'), 0, 0)

      const endDateTime = new Date(startDateTime)
      endDateTime.setHours(startDateTime.getHours() + 1) // 1 hour session

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        alert('Please log in to book an appointment.')
        return
      }

      // Check for double booking
      const { data: existingBookings, error: checkError } = await supabase
        .from('bookings')
        .select('id')
        .eq('provider_id', provider?.id)
        .eq('start_time', startDateTime.toISOString())
        .eq('status', 'pending')

      if (checkError) {
        console.error('Error checking existing bookings:', checkError)
        alert('Error checking availability. Please try again.')
        return
      }

      if (existingBookings && existingBookings.length > 0) {
        alert('This time slot is already booked. Please select a different time.')
        return
      }

      // Save to database
      const { data, error } = await supabase
        .from('bookings')
        .insert({
          user_id: user.id,
          provider_id: provider?.id,
          provider_type: providerType || 'caregiver',
          start_time: startDateTime.toISOString(),
          end_time: endDateTime.toISOString(),
          status: 'pending',
          total_cost: provider?.hourly_rate,
          payment_status: 'pending',
          notes: `Booking for ${provider?.name} on ${selectedDate} at ${selectedTime}`
        })

      if (error) {
        console.error('Error saving booking:', error)
        alert('Error saving booking. Please try again.')
        return
      }

      console.log('Booking saved successfully:', data)

      // Create notifications for booking confirmation
      if (data && Array.isArray(data) && data.length > 0) {
        await createBookingNotifications(user.id, provider?.id, (data as any)[0].id)
      }

      setBookingStep(4)
    } catch (error) {
      console.error('Error in saveBookingToDatabase:', error)
      setBookingError('Failed to book appointment. Please try again.')
    } finally {
      setIsBooking(false)
    }
  }

  const createBookingNotifications = async (userId: string, providerId: string | undefined, bookingId: string) => {
    try {
      const notifications = [
        // Notification for the user (booking confirmation)
        {
          user_id: userId,
          type: 'booking_confirmation',
          title: 'Booking Confirmed',
          content: `Your appointment with ${provider?.name} has been confirmed for ${selectedDate} at ${selectedTime}.`,
          related_entity_id: bookingId,
          related_table: 'bookings',
          related_entity_type: 'booking',
          metadata: {
            booking_date: selectedDate,
            booking_time: selectedTime,
            provider_name: provider?.name
          }
        }
      ]

      // Add notification for provider if providerId exists
      if (providerId) {
        notifications.push({
          user_id: providerId,
          type: 'new_booking',
          title: 'New Booking Request',
          content: `You have a new booking request for ${selectedDate} at ${selectedTime}.`,
          related_entity_id: bookingId,
          related_table: 'bookings',
          related_entity_type: 'booking',
          metadata: {
            booking_date: selectedDate,
            booking_time: selectedTime,
            provider_name: provider?.name
          }
        })
      }

      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notifications)

      if (notificationError) {
        console.error('Error creating notifications:', notificationError)
        // Don't fail the booking if notifications fail
      } else {
        console.log('Booking notifications created successfully')
      }
    } catch (error) {
      console.error('Error in createBookingNotifications:', error)
      // Don't fail the booking if notifications fail
    }
  }

  const handleSendMessage = () => {
    // TODO: Implement actual messaging functionality
    console.log(`Opening chat with ${provider?.name}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{ backgroundColor: 'var(--bg-primary)' }}>
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-32 h-32 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)' }}></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)', animationDelay: '1s' }}></div>
        </div>

        <div className="text-center max-w-md mx-auto p-8 relative z-10 animate-fadeInUp">
          <div className="w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
            <div className="w-8 h-8 border-4 rounded-full animate-spin border-t-transparent" style={{ borderColor: 'var(--primary)' }}></div>
          </div>
          <h2 className="text-2xl font-light mb-2" style={{ color: 'var(--text-primary)' }}>Loading Profile</h2>
          <p style={{ color: 'var(--text-secondary)' }}>Preparing provider information...</p>
        </div>
      </div>
    )
  }

  if (error || !provider) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{ backgroundColor: 'var(--bg-primary)' }} role="main" aria-label="Provider profile error">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-32 h-32 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)' }}></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)', animationDelay: '1s' }}></div>
        </div>

        <div className="text-center max-w-md mx-auto p-8 relative z-10 animate-fadeInUp">
          <div className="w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg" style={{ backgroundColor: 'var(--bg-error-light)' }}>
            <div className="w-8 h-8" style={{ color: 'var(--error)' }}>⚠️</div>
          </div>
          <h2 className="text-2xl font-light mb-2" style={{ color: 'var(--text-primary)' }}>Profile Not Found</h2>
          <p className="mb-6" style={{ color: 'var(--text-secondary)' }} role="alert">{error || 'Provider not found'}</p>
          <button
            onClick={() => navigate(-1)}
            className="button-primary px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:opacity-90"
            aria-label="Go back to previous page"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }} role="main" aria-label="Provider profile">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Enhanced Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="button-secondary flex items-center gap-3 mb-8 px-4 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-md group hover:opacity-90"
          aria-label={`Go back to ${providerType?.replace('-', ' ')} listings`}
        >
          <ArrowLeft className="h-5 w-5 group-hover:text-primary transition-colors" aria-hidden="true" />
          <span className="font-medium group-hover:text-primary transition-colors">Back to {providerType?.replace('-', ' ')}</span>
        </button>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Enhanced Provider Info */}
          <div className="lg:col-span-2">
            <div className="rounded-2xl p-8 animate-fadeInUp" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
              {/* Enhanced Header */}
              <div className="flex items-start gap-6 mb-8">
                <div className="w-24 h-24 rounded-2xl flex items-center justify-center shadow-lg ring-4 ring-accent transition-all duration-300" style={{ backgroundColor: 'var(--bg-accent)' }} role="img" aria-label={`Profile picture for ${provider.name}`}>
                  <span className="text-3xl font-bold" style={{ color: 'var(--primary)' }}>
                    {provider.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <h1 className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>{provider.name}</h1>
                    {provider.verified && (
                      <CheckCircle className="h-6 w-6" style={{ color: 'var(--primary)' }} aria-label="Verified provider" />
                    )}
                  </div>
                  <div className="flex items-center gap-6 mb-4">
                    <div className="flex items-center gap-2 px-3 py-2 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }} role="group" aria-label={`Rating: ${provider.rating ? provider.rating.toFixed(1) : 'No rating'} out of 5 stars`}>
                      <Star className="h-5 w-5" style={{ color: 'var(--accent-warning)', fill: 'var(--accent-warning)' }} aria-hidden="true" />
                      <span className="font-bold" style={{ color: 'var(--text-primary)' }}>{provider.rating ? provider.rating.toFixed(1) : 'No rating'}</span>
                      <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>({provider.reviews_count || 0} reviews)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" style={{ color: 'var(--text-muted)' }} aria-hidden="true" />
                      <span className="font-medium" style={{ color: 'var(--text-secondary)' }}>{provider.location}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-6">
                    <div className="px-4 py-2 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
                      <span className="text-lg font-bold" style={{ color: 'var(--primary)' }}>
                        ${provider.hourly_rate}/hour
                      </span>
                    </div>
                    <span className="font-medium" style={{ color: 'var(--text-secondary)' }}>
                      {provider.years_experience} years experience
                    </span>
                  </div>
                </div>
              </div>

              {/* Bio */}
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3 macos-subtitle" style={{ color: 'var(--text-primary)' }}>About</h2>
                <p className="leading-relaxed macos-body" style={{ color: 'var(--text-secondary)' }}>{provider.bio}</p>
              </div>

              {/* Specialties */}
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3 macos-subtitle" style={{ color: 'var(--text-primary)' }}>Specialties</h2>
                <div className="flex flex-wrap gap-2" role="list" aria-label="Provider specialties">
                  {provider.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 rounded-full text-sm macos-body"
                      style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)' }}
                      role="listitem"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>

              {/* Additional Information */}
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3 macos-subtitle" style={{ color: 'var(--text-primary)' }}>Additional Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                    <h3 className="font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>Background Check</h3>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" style={{ color: 'var(--primary)' }} aria-hidden="true" />
                      <span className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>Verified & Current</span>
                    </div>
                  </div>
                  <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                    <h3 className="font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>Languages</h3>
                    <p className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>English, Spanish</p>
                  </div>
                  <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                    <h3 className="font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>Insurance</h3>
                    <p className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>Liability & Bonded</p>
                  </div>
                  <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                    <h3 className="font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>Availability</h3>
                    <p className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>Weekdays & Weekends</p>
                  </div>
                </div>
              </div>

              {/* Contact Actions */}
              <div className="mb-6">
                <div className="flex gap-3">
                  <button className="flex-1 px-4 py-3 rounded-lg transition-colors flex items-center justify-center gap-2 macos-body"
                          style={{ backgroundColor: 'var(--primary)', color: 'var(--text-white)' }}
                          onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                          onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                          aria-label={`Send message to ${provider.name}`}>
                    <MessageCircle className="h-4 w-4" aria-hidden="true" />
                    Send Message
                  </button>
                  <button className="flex-1 px-4 py-3 rounded-lg transition-colors macos-body"
                          style={{ border: '1px solid var(--border-medium)', color: 'var(--text-primary)', backgroundColor: 'var(--bg-primary)' }}
                          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
                          aria-label={`Save ${provider.name}'s profile`}>
                    Save Profile
                  </button>
                </div>
              </div>

              {/* Reviews Section */}
              <div>
                <h2 className="text-lg font-semibold mb-3 macos-subtitle" style={{ color: 'var(--text-primary)' }}>Recent Reviews</h2>
                <div className="space-y-4">
                  <div className="text-center py-8" role="status" aria-live="polite">
                    <div className="mb-2" style={{ color: 'var(--text-secondary)' }}>
                      <Star className="h-8 w-8 mx-auto mb-2 opacity-50" aria-hidden="true" />
                    </div>
                    <p className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>
                      Reviews are loaded from our database.
                    </p>
                    <p className="text-xs mt-1 macos-body" style={{ color: 'var(--text-muted)' }}>
                      Contact provider directly to see their full review history.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Booking Panel */}
          <div className="xl:col-span-1">
            <div className="rounded-2xl p-8 sticky top-8 animate-fadeInUp" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }} role="form" aria-label="Book appointment form">
              <h2 className="text-2xl font-bold mb-6" style={{ color: 'var(--text-primary)' }}>Book Appointment</h2>

              {/* Booking Progress Indicator */}
              <div className="flex items-center justify-between mb-6" role="progressbar" aria-valuenow={bookingStep} aria-valuemin={1} aria-valuemax={4} aria-label={`Booking step ${bookingStep} of 4`}>
                {[1, 2, 3, 4].map((step) => (
                  <div key={step} className="flex items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        step <= bookingStep ? 'text-white' : 'text-gray-400'
                      }`}
                      style={{
                        backgroundColor: step <= bookingStep ? 'var(--primary)' : 'var(--border-medium)',
                        color: step <= bookingStep ? 'var(--text-white)' : 'var(--text-muted)'
                      }}
                    >
                      {step}
                    </div>
                    {step < 4 && (
                      <div
                        className="w-8 h-0.5 mx-2"
                        style={{ backgroundColor: step < bookingStep ? 'var(--primary)' : 'var(--border-medium)' }}
                      />
                    )}
                  </div>
                ))}
              </div>

              {/* Error Display */}
              {bookingError && (
                <div
                  className="mb-4 p-3 rounded-lg border"
                  style={{
                    backgroundColor: 'var(--bg-error-light)',
                    borderColor: 'var(--error)',
                    color: 'var(--error)'
                  }}
                  role="alert"
                  aria-live="polite"
                >
                  {bookingError}
                </div>
              )}
              
              {bookingStep === 1 && (
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Select Date
                  </label>
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:outline-none mb-4"
                    style={{
                      borderColor: 'var(--border-medium)',
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--text-primary)'
                    }}
                  />
                  <button
                    onClick={handleBookAppointment}
                    disabled={!selectedDate}
                    className="w-full py-3 px-4 rounded-lg font-medium mb-3 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    style={{ backgroundColor: selectedDate ? 'var(--primary)' : 'var(--text-muted)', color: 'var(--text-white)' }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '0.9')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '1')}
                  >
                    Continue
                  </button>
                </div>
              )}

              {bookingStep === 2 && (
                <div>
                  <button
                    onClick={() => setBookingStep(1)}
                    className="text-sm mb-3 flex items-center gap-1 transition-colors"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    <ArrowLeft className="h-3 w-3" />
                    Change date
                  </button>
                  <p className="text-sm mb-3" style={{ color: 'var(--text-secondary)' }}>Date: {selectedDate}</p>
                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Select Time
                  </label>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {availableTimeSlots.map((slot) => (
                      <button
                        key={slot}
                        onClick={() => setSelectedTime(slot)}
                        className="p-2 text-sm rounded border transition-colors"
                        aria-label={`Select ${slot} appointment time`}
                        aria-pressed={selectedTime === slot}
                        style={{
                          backgroundColor: selectedTime === slot ? 'var(--primary)' : 'var(--bg-primary)',
                          color: selectedTime === slot ? 'var(--text-white)' : 'var(--text-primary)',
                          borderColor: selectedTime === slot ? 'var(--primary)' : 'var(--border-medium)'
                        }}
                        onMouseEnter={(e) => {
                          if (selectedTime !== slot) {
                            e.currentTarget.style.borderColor = 'var(--primary)'
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (selectedTime !== slot) {
                            e.currentTarget.style.borderColor = 'var(--border-medium)'
                          }
                        }}
                      >
                        {slot}
                      </button>
                    ))}
                  </div>
                  <button
                    onClick={handleBookAppointment}
                    disabled={!selectedTime}
                    className="w-full py-3 px-4 rounded-lg font-medium mb-3 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    style={{ backgroundColor: selectedTime ? 'var(--primary)' : 'var(--text-muted)', color: 'var(--text-white)' }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '0.9')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '1')}
                  >
                    Book Appointment
                  </button>
                </div>
              )}

              {bookingStep === 3 && (
                <div>
                  <button
                    onClick={() => setBookingStep(2)}
                    className="text-sm mb-3 flex items-center gap-1 transition-colors"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    <ArrowLeft className="h-3 w-3" />
                    Change time
                  </button>
                  <h3 className="text-lg font-semibold mb-3" style={{ color: 'var(--text-primary)' }}>Payment Information</h3>
                  <p className="text-sm mb-3" style={{ color: 'var(--text-secondary)' }}>
                    Appointment: {selectedDate} at {selectedTime}
                  </p>
                  <p className="text-sm font-medium mb-4" style={{ color: 'var(--text-primary)' }}>
                    Total: ${provider?.hourly_rate ? `${provider.hourly_rate}.00` : 'Contact for pricing'}
                  </p>

                  <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                    Payment Method
                  </label>
                  <select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:outline-none mb-4"
                    style={{
                      borderColor: 'var(--border-medium)',
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--text-primary)'
                    }}
                  >
                    <option value="">Select payment method</option>
                    <option value="credit_card">Credit Card</option>
                    <option value="debit_card">Debit Card</option>
                    <option value="paypal">PayPal</option>
                    <option value="insurance">Insurance</option>
                  </select>

                  <button
                    onClick={handleBookAppointment}
                    disabled={!paymentMethod || isBooking}
                    className="w-full py-3 px-4 rounded-lg font-medium mb-3 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                    style={{ backgroundColor: (paymentMethod && !isBooking) ? 'var(--primary)' : 'var(--text-muted)', color: 'var(--text-white)' }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '0.9')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '1')}
                    aria-label={isBooking ? 'Processing booking...' : 'Confirm and pay for appointment'}
                  >
                    {isBooking ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Processing...
                      </>
                    ) : (
                      'Confirm & Pay'
                    )}
                  </button>
                </div>
              )}

              {bookingStep === 4 && (
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 mx-auto mb-3" style={{ color: 'var(--primary)' }} />
                  <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Booking Confirmed!</h3>
                  <p className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                    Your appointment with {provider.name} is scheduled for {selectedDate} at {selectedTime}.
                  </p>
                  <p className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                    Payment method: {paymentMethod?.replace('_', ' ')}
                  </p>
                  <button
                    onClick={() => navigate(-1)}
                    className="w-full py-2 px-4 rounded-lg font-medium transition-colors"
                    style={{ backgroundColor: 'var(--primary)', color: 'var(--text-white)' }}
                    onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                    onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                  >
                    Back to Search
                  </button>
                </div>
              )}

              {bookingStep < 4 && (
                <>
                  <button
                    onClick={handleSendMessage}
                    className="w-full py-2 px-4 rounded-lg font-medium mb-3 flex items-center justify-center gap-2 transition-colors"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--primary)',
                      border: '1px solid var(--primary)'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
                  >
                    <MessageCircle className="h-4 w-4" />
                    Send Message
                  </button>

                  <div className="text-xs text-center" style={{ color: 'var(--text-muted)' }}>
                    <Clock className="h-3 w-3 inline mr-1" />
                    Usually responds within 2 hours
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
