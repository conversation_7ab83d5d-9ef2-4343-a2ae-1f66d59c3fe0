import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Calendar as CalendarIcon, UserCheck, MessageSquare, Eye, Plus, Filter, Search, Lock } from 'lucide-react';
import { format, parseISO, isAfter, isBefore } from 'date-fns';

interface Booking {
  id: string;
  user_id: string;
  service_provider_id: string;
  service_details: string;
  booking_start_time: string;
  booking_end_time: string;
  status: string;
  total_cost: number;
  notes?: string;
  cancellation_reason?: string;
  duration_hours: string;
  payment_status: string;
  created_at: string;
  updated_at: string;
}

const MyBookingsPage: React.FC = () => {
  const navigate = useNavigate();
  
  const [user, setUser] = useState<any>(null);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'past' | 'cancelled'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchBookings = async () => {
      setLoading(true);
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        setUser(user);
        
        if (!user) {
          setError("Please sign in to view your bookings.");
          setLoading(false);
          return;
        }

        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        console.log('Bookings data fetched successfully:', data);
        setBookings(data || []);
      } catch (err: any) {
        console.error("Error fetching bookings:", err.message);
        setError(err.message || "Failed to load bookings.");
        setBookings([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, []);

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'confirmed':
        return { backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' };
      case 'pending':
        return { backgroundColor: 'var(--warning)', color: 'var(--bg-primary)' };
      case 'cancelled_by_user':
      case 'cancelled_by_provider':
        return { backgroundColor: 'var(--error)', color: 'var(--bg-primary)' };
      case 'completed':
        return { backgroundColor: 'var(--success)', color: 'var(--bg-primary)' };
      default:
        return { backgroundColor: 'var(--text-muted)', color: 'var(--bg-primary)' };
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'cancelled_by_user':
        return 'Cancelled by You';
      case 'cancelled_by_provider':
        return 'Cancelled by Provider';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  };

  const getFilteredBookings = () => {
    let filtered = bookings;

    // Apply status filter
    if (filter === 'upcoming') {
      filtered = filtered.filter(booking => 
        isAfter(parseISO(booking.booking_start_time), new Date()) && 
        !['cancelled_by_user', 'cancelled_by_provider', 'completed'].includes(booking.status)
      );
    } else if (filter === 'past') {
      filtered = filtered.filter(booking => 
        isBefore(parseISO(booking.booking_start_time), new Date()) || 
        booking.status === 'completed'
      );
    } else if (filter === 'cancelled') {
      filtered = filtered.filter(booking => 
        ['cancelled_by_user', 'cancelled_by_provider'].includes(booking.status)
      );
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.service_details.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (booking.notes && booking.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading your bookings...</span>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header with Navigation */}
        <div className="border-b" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
                <p className="text-gray-600 mt-1">View and manage your care service appointments</p>
              </div>
              <Link
                to="/dashboard"
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Authentication Error State */}
        <div className="auth-error-container">
          <div className="auth-error-content">
            <Lock className="auth-error-icon" />
            <h2 className="auth-error-title">Access Restricted</h2>
            <p className="auth-error-message">Please sign in to view and manage your care service appointments.</p>
            <div className="auth-error-buttons">
              <button
                onClick={() => window.location.href = '/auth'}
                className="button-primary"
              >
                Sign In
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="button-secondary"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header with Navigation */}
        <div className="border-b" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
                <p className="text-gray-600 mt-1">Manage your care service appointments</p>
              </div>
              <Link
                to="/dashboard"
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Error State */}
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 text-var(--text-muted) mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Bookings</h2>
            <p className="text-var(--text-muted) mb-6 max-w-md mx-auto">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 rounded-xl font-medium transition-opacity mr-4"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
              onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
            >
              Try Again
            </button>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-medium hover:bg-gray-200 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  const filteredBookings = getFilteredBookings();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Bookings</h1>
              <p className="text-gray-600">View and manage your care appointments</p>
            </div>
            <Link
              to="/find-care"
              className="flex items-center px-6 py-3 rounded-xl font-medium transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary-dark)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
            >
              <Plus className="mr-2 h-4 w-4" />
              Book New Service
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Filters and Search */}
        <div className="rounded-2xl shadow-sm border p-6 mb-8" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="flex flex-col sm:flex-row gap-4">
            
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search bookings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                />
              </div>
            </div>

            {/* Filter Buttons */}
            <div className="flex gap-2">
              {(['all', 'upcoming', 'past', 'cancelled'] as const).map((filterOption) => (
                <button
                  key={filterOption}
                  onClick={() => setFilter(filterOption)}
                  className={`px-4 py-3 rounded-xl font-medium transition-colors capitalize ${
                    filter === filterOption
                      ? ''
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  style={filter === filterOption ? { backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' } : {}}
                >
                  {filterOption}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Bookings List */}
        {filteredBookings.length === 0 ? (
          <div className="rounded-2xl shadow-sm border p-12" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
            <div className="text-center">
              <CalendarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {bookings.length === 0 ? 'No Bookings Yet' : 'No Matching Bookings'}
              </h3>
              <p className="text-gray-600 mb-6">
                {bookings.length === 0 
                  ? 'Start by booking your first care service.'
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
              {bookings.length === 0 && (
                <Link
                  to="/find-care"
                  className="inline-flex items-center px-6 py-3 rounded-xl font-medium transition-colors"
                  style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                  onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                  onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Book Your First Service
                </Link>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="rounded-2xl shadow-sm border p-6 hover:shadow-md transition-shadow" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <div className="flex items-center justify-between">
                  
                  {/* Booking Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-3">
                      <div className="h-12 w-12 rounded-full bg-var(--logo-green) bg-opacity-10 flex items-center justify-center">
                        <UserCheck className="h-6 w-6 text-var(--logo-green)" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{booking.service_details}</h3>
                        <p className="text-gray-600">
                          Service Provider Booking
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Date & Time:</span>
                        <p className="font-medium text-gray-900">
                          {format(parseISO(booking.booking_start_time), 'MMM d, yyyy')}
                        </p>
                        <p className="text-gray-600">
                          {format(parseISO(booking.booking_start_time), 'h:mm a')} - {format(parseISO(booking.booking_end_time), 'h:mm a')}
                        </p>
                      </div>
                      
                      <div>
                        <span className="text-gray-500">Total Cost:</span>
                        <p className="font-medium text-gray-900">${booking.total_cost}</p>
                      </div>
                      
                      <div>
                        <span className="text-gray-500">Duration:</span>
                        <p className="font-medium text-gray-900">{booking.duration_hours} hours</p>
                      </div>
                    </div>
                  </div>

                  {/* Status and Actions */}
                  <div className="flex flex-col items-end gap-3">
                    <div className="px-3 py-1 rounded-full text-xs font-medium" style={getStatusBadgeStyle(booking.status)}>
                      {getStatusLabel(booking.status)}
                    </div>
                    
                    <div className="flex gap-2">
                      <Link
                        to={`/booking/${booking.id}`}
                        className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Link>
                      
                      {booking.status === 'confirmed' && (
                        <Link
                          to={`/messages/new?recipientId=${booking.service_provider_id}&providerName=${encodeURIComponent('Service Provider')}`}
                          className="flex items-center px-4 py-2 rounded-lg font-medium transition-colors"
                          style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary-dark)'}
                          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
                        >
                          <MessageSquare className="mr-2 h-4 w-4" />
                          Message
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary Stats */}
        {bookings.length > 0 && (
          <div className="mt-8 rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h3>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-var(--logo-green)">{bookings.length}</p>
                <p className="text-sm text-gray-600">Total Bookings</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {bookings.filter(b => b.status === 'confirmed').length}
                </p>
                <p className="text-sm text-gray-600">Confirmed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {bookings.filter(b => b.status === 'completed').length}
                </p>
                <p className="text-sm text-gray-600">Completed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-600">
                  ${bookings.filter(b => b.status === 'completed').reduce((sum, b) => sum + b.total_cost, 0)}
                </p>
                <p className="text-sm text-gray-600">Total Spent</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MyBookingsPage;
