import { useState, useEffect, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, MapPin, Star, Clock, Users, MessageCircle, Shield, Heart } from 'lucide-react'
import { dataService } from '../lib/dataService'

console.log('🔍 CAREGIVERS COMPONENT FILE LOADED - imports complete')

interface Caregiver {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  rating?: number
  reviews_count?: number
  availability_status?: string
}

export default function Caregivers() {
  const navigate = useNavigate()
  const [caregivers, setCaregivers] = useState<Caregiver[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchName, setSearchName] = useState('')
  const [searchLocation, setSearchLocation] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [maxRate, setMaxRate] = useState(200)
  const [favorites, setFavorites] = useState<string[]>([])
  const [imageErrors, setImageErrors] = useState<string[]>([])

  const toggleFavorite = (caregiverId: string) => {
    setFavorites(prev =>
      prev.includes(caregiverId)
        ? prev.filter(id => id !== caregiverId)
        : [...prev, caregiverId]
    )
  }

  const handleImageError = (caregiverId: string) => {
    setImageErrors(prev => [...prev, caregiverId])
  }

  const handleSearch = () => {
    console.log('Search triggered with:', { searchName, searchLocation })
  }

  // Fetch caregivers data
  useEffect(() => {
    console.log('🔍 CAREGIVERS COMPONENT MOUNTED - useEffect triggered')

    const fetchCaregivers = async () => {
      try {
        console.log('🔍 Fetching caregivers from database...')
        const data = await dataService.getCaregivers()
        console.log('🔍 Caregivers data received:', data?.length || 0, 'items')
        setCaregivers(data || [])
      } catch (err) {
        console.error('🔍 Error loading caregivers:', err)
        setError(err instanceof Error ? err.message : 'Failed to load caregivers')
        setCaregivers([])
      } finally {
        setLoading(false)
      }
    }

    fetchCaregivers()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <span className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>Loading caregivers...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <span className="text-lg font-medium" style={{ color: 'var(--error)' }}>Error: {error}</span>
          </div>
        </div>
      </div>
    )
  }

  // Memoized filter and sort caregivers for performance
  const filteredCaregivers = useMemo(() => {
    return caregivers
      .filter(caregiver => {
        const matchesName = !searchName || caregiver.name.toLowerCase().includes(searchName.toLowerCase())
        const matchesLocation = !searchLocation || caregiver.location.toLowerCase().includes(searchLocation.toLowerCase())
        const matchesRate = !caregiver.hourly_rate || caregiver.hourly_rate <= maxRate
        return matchesName && matchesLocation && matchesRate
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'rating':
            return (b.rating || 0) - (a.rating || 0)
          case 'experience':
            return (b.years_experience || 0) - (a.years_experience || 0)
          case 'rate':
            return (a.hourly_rate || 0) - (b.hourly_rate || 0)
          default:
            return a.name.localeCompare(b.name)
        }
      })
  }, [caregivers, searchName, searchLocation, maxRate, sortBy])

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header Section */}
      <div className="px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>
            Find Your Perfect Caregiver
          </h1>
          <p className="text-xl mb-4 max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
            Connect with experienced, verified caregivers in your area
          </p>
        </div>
      </div>

      {/* Search Section */}
      <div className="px-8 py-8" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Name Search */}
            <div className="relative">
              <label htmlFor="search-name" className="sr-only">Search caregivers by name</label>
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} />
              <input
                id="search-name"
                type="text"
                placeholder="Search caregivers..."
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-xl border-2 transition-all duration-200 focus:outline-none"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  color: 'var(--text-primary)'
                }}
                aria-label="Search caregivers by name"
              />
            </div>

            {/* Location Search */}
            <div className="relative">
              <label htmlFor="search-location" className="sr-only">Search by location</label>
              <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} />
              <input
                id="search-location"
                type="text"
                placeholder="Enter location..."
                value={searchLocation}
                onChange={(e) => setSearchLocation(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-xl border-2 transition-all duration-200 focus:outline-none"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  color: 'var(--text-primary)'
                }}
                aria-label="Search caregivers by location"
              />
            </div>

            {/* Sort Dropdown */}
            <div className="relative">
              <label htmlFor="sort-by" className="sr-only">Sort caregivers by</label>
              <select
                id="sort-by"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-4 rounded-xl border-2 transition-all duration-200 focus:outline-none appearance-none cursor-pointer"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  color: 'var(--text-primary)'
                }}
                aria-label="Sort caregivers by criteria"
              >
                <option value="name">Name (A-Z)</option>
                <option value="rating">Highest Rated</option>
                <option value="experience">Most Experienced</option>
                <option value="rate">Lowest Rate</option>
              </select>
            </div>

            {/* Max Rate Filter */}
            <div className="relative">
              <label htmlFor="max-rate" className="sr-only">Maximum hourly rate filter</label>
              <input
                id="max-rate"
                type="range"
                min="20"
                max="200"
                step="10"
                value={maxRate}
                onChange={(e) => setMaxRate(Number(e.target.value))}
                className="w-full h-3 rounded-lg appearance-none cursor-pointer"
                aria-label={`Maximum hourly rate: $${maxRate}`}
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                  $0/hr
                </span>
                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  Max: ${maxRate}/hr
                </span>
              </div>
            </div>
          </div>

          {/* Search Button */}
          <div className="text-center">
            <button
              onClick={handleSearch}
              className="inline-flex items-center gap-3 px-8 py-4 rounded-xl font-bold text-white transition-all duration-300"
              style={{ backgroundColor: 'var(--primary)' }}
              aria-label="Search for caregivers with current filters"
            >
              <Search className="w-5 h-5" />
              Search Caregivers
            </button>
          </div>
        </div>
      </div>

      {/* Results Section */}
      <main className="px-8 py-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
              Available Caregivers
            </h2>
            <p style={{ color: 'var(--text-secondary)' }}>
              {filteredCaregivers.length} caregiver{filteredCaregivers.length !== 1 ? 's' : ''} found
            </p>
          </div>

          {filteredCaregivers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto mb-4 w-16 h-16" style={{ color: 'var(--text-muted)' }} />
              <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>No caregivers found</h3>
              <p style={{ color: 'var(--text-secondary)' }}>Try adjusting your search criteria or check back later</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCaregivers.map((caregiver) => (
                <div
                  key={caregiver.id}
                  className="rounded-2xl p-6 transition-all duration-300 hover:shadow-lg focus-within:ring-2 focus-within:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '2px solid var(--border-light)'
                  } as React.CSSProperties}
                  tabIndex={0}
                  role="article"
                  aria-label={`Caregiver profile for ${caregiver.name}`}
                >
                  <div className="flex items-start mb-4">
                    <div className="flex-shrink-0 mr-4">
                      {caregiver.profile_image && !imageErrors.includes(caregiver.id) ? (
                        <img
                          src={caregiver.profile_image}
                          alt={caregiver.name}
                          className="w-16 h-16 rounded-xl object-cover"
                          onError={() => handleImageError(caregiver.id)}
                        />
                      ) : (
                        <div className="w-16 h-16 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)' }}>
                          <Shield className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold mb-1" style={{ color: 'var(--text-primary)' }}>
                        {caregiver.name}
                      </h3>
                      <div className="flex items-center mb-2">
                        <MapPin className="w-4 h-4 mr-1" style={{ color: 'var(--text-muted)' }} />
                        <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                          {caregiver.location}
                        </span>
                      </div>
                      {caregiver.rating && (
                        <div className="flex items-center">
                          <Star className="w-4 h-4 mr-1 fill-current" style={{ color: 'var(--accent-warning)' }} />
                          <span className="text-sm font-bold" style={{ color: 'var(--text-primary)' }}>
                            {caregiver.rating.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <p className="text-sm mb-4 line-clamp-2" style={{ color: 'var(--text-secondary)' }}>
                    {caregiver.bio}
                  </p>

                  {caregiver.specialties && caregiver.specialties.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2 min-h-[32px] items-center">
                        {caregiver.specialties.slice(0, 2).map((specialty: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 rounded-lg text-xs font-medium flex-shrink-0"
                            style={{
                              backgroundColor: 'var(--bg-accent)',
                              color: 'var(--primary)'
                            }}
                          >
                            {specialty.length > 12 ? `${specialty.substring(0, 12)}...` : specialty}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between mb-4">
                    {caregiver.hourly_rate && (
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" style={{ color: 'var(--primary)' }} />
                        <span className="font-bold" style={{ color: 'var(--primary)' }}>${caregiver.hourly_rate}/hr</span>
                      </div>
                    )}
                    {caregiver.years_experience && (
                      <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        {caregiver.years_experience}+ years
                      </span>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <button
                      className="flex-1 py-2 px-4 rounded-lg font-medium text-white transition-all duration-200"
                      style={{ backgroundColor: 'var(--primary)' }}
                      onClick={() => navigate(`/provider/caregivers/${caregiver.id}`)}
                    >
                      View Profile
                    </button>
                    <button
                      className="p-2 rounded-lg transition-all duration-200"
                      style={{
                        backgroundColor: favorites.includes(caregiver.id) ? 'var(--bg-error-light)' : 'var(--bg-accent)',
                        color: favorites.includes(caregiver.id) ? 'var(--error)' : 'var(--primary)'
                      }}
                      onClick={() => toggleFavorite(caregiver.id)}
                      aria-label={favorites.includes(caregiver.id) ? `Remove ${caregiver.name} from favorites` : `Add ${caregiver.name} to favorites`}
                    >
                      <Heart className={`w-5 h-5 ${favorites.includes(caregiver.id) ? 'fill-current' : ''}`} />
                    </button>
                    <button
                      className="p-2 rounded-lg transition-all duration-200"
                      style={{
                        backgroundColor: 'var(--bg-accent)',
                        color: 'var(--primary)'
                      }}
                      onClick={() => navigate('/messages', { state: { startConversationWith: caregiver.id, providerName: caregiver.name } })}
                      aria-label={`Send message to ${caregiver.name}`}
                    >
                      <MessageCircle className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
