import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Calendar as CalendarIcon, User, Clock, MapPin, DollarSign, ChevronLeft, ChevronRight, Plus, Filter, Eye, Edit3, X } from 'lucide-react';
import { format, parseISO, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths, startOfWeek, endOfWeek, isToday } from 'date-fns';

interface ProviderBooking {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  created_at: string;
  user_name?: string;
  user_email?: string;
  user_phone?: string;
}

interface AvailabilitySlot {
  id: string;
  provider_id: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  created_at: string;
}

const ProviderBookingCalendarPage: React.FC = () => {
  const { providerId } = useParams<{ providerId: string }>();
  const navigate = useNavigate();
  
  const [bookings, setBookings] = useState<ProviderBooking[]>([]);
  const [availability, setAvailability] = useState<AvailabilitySlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
  const [providerInfo, setProviderInfo] = useState<any>(null);

  // Filter states
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  const statusOptions = [
    { value: 'all', label: 'All Bookings' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled_by_user', label: 'Cancelled by User' },
    { value: 'cancelled_by_provider', label: 'Cancelled by Provider' }
  ];

  useEffect(() => {
    const fetchCalendarData = async () => {
      if (!providerId) {
        setError("Invalid provider ID.");
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to view provider calendar.");
          setLoading(false);
          return;
        }

        // Fetch provider info
        const { data: providerData, error: providerError } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('full_name, email, phone, role')
          .eq('id', providerId)
          .single();

        if (providerError) {
          console.error('Provider fetch error:', providerError);
          throw new Error('Provider not found');
        }

        setProviderInfo(providerData);

        // Fetch bookings for this provider
        const startDate = startOfMonth(subMonths(currentDate, 1));
        const endDate = endOfMonth(addMonths(currentDate, 2));

        const { data: bookingsData, error: bookingsError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('provider_id', providerId)
          .gte('start_time', startDate.toISOString())
          .lte('start_time', endDate.toISOString())
          .order('start_time', { ascending: true });

        if (bookingsError) {
          console.error('Bookings fetch error:', bookingsError);
          throw bookingsError;
        }

        // Fetch user details for each booking
        const bookingsWithUsers = await Promise.all(
          (bookingsData || []).map(async (booking) => {
            const { data: userData, error: userError } = await supabase
              .schema('care_connector')
              .from('profiles')
              .select('full_name, email, phone')
              .eq('id', booking.user_id)
              .single();

            if (userError) {
              console.warn('Could not fetch user details:', userError);
            }

            return {
              ...booking,
              user_name: userData?.full_name || 'User',
              user_email: userData?.email || '',
              user_phone: userData?.phone || ''
            };
          })
        );

        // Fetch availability slots (if they exist)
        const { data: availabilityData, error: availabilityError } = await supabase
          .schema('care_connector')
          .from('provider_availability')
          .select('*')
          .eq('provider_id', providerId)
          .gte('start_time', startDate.toISOString())
          .lte('start_time', endDate.toISOString())
          .order('start_time', { ascending: true });

        if (availabilityError) {
          console.warn('Availability fetch error (table may not exist):', availabilityError);
          setAvailability([]);
        } else {
          setAvailability(availabilityData || []);
        }

        console.log('Calendar data fetched successfully:', { bookingsWithUsers, availabilityData });
        setBookings(bookingsWithUsers);

      } catch (err: any) {
        console.error("Error fetching calendar data:", err.message);
        setError(err.message || "Failed to load calendar data.");
        setBookings([]);
        setAvailability([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCalendarData();
  }, [providerId, currentDate]);

  const getBookingsForDate = (date: Date) => {
    return bookings.filter(booking => {
      if (selectedStatus !== 'all' && booking.status !== selectedStatus) {
        return false;
      }
      return isSameDay(parseISO(booking.start_time), date);
    });
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      confirmed: 'bg-blue-100 text-blue-800 border-blue-200',
      completed: 'bg-green-100 text-green-800 border-green-200',
      cancelled_by_user: 'bg-red-100 text-red-800 border-red-200',
      cancelled_by_provider: 'bg-red-100 text-red-800 border-red-200'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(direction === 'prev' ? subMonths(currentDate, 1) : addMonths(currentDate, 1));
  };

  const generateCalendarDays = () => {
    const start = startOfWeek(startOfMonth(currentDate));
    const end = endOfWeek(endOfMonth(currentDate));
    return eachDayOfInterval({ start, end });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading calendar...</span>
        </div>
      </div>
    );
  }

  if (error || !providerInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Calendar</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <div className="flex gap-3">
              <button
                onClick={() => navigate('/dashboard')}
                className="button-primary flex-1 px-4 py-3 rounded-xl font-medium transition-colors hover:opacity-90"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const calendarDays = generateCalendarDays();
  const selectedDateBookings = selectedDate ? getBookingsForDate(selectedDate) : [];

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-var(--logo-green) mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Provider Calendar</h1>
                <p className="text-gray-600">{providerInfo.full_name} - {providerInfo.role?.replace('_', ' ').toUpperCase()}</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="button-secondary flex items-center px-4 py-2 rounded-xl transition-colors hover:opacity-90"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </button>
              <button
                onClick={() => setShowAvailabilityModal(true)}
                className="flex items-center px-4 py-2 bg-var(--logo-green) text-white rounded-xl hover:bg-green-600 transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Availability
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Filters */}
        {showFilters && (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Bookings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Calendar */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              
              {/* Calendar Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {format(currentDate, 'MMMM yyyy')}
                </h2>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => navigateMonth('prev')}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <ChevronLeft className="h-5 w-5 text-gray-600" />
                  </button>
                  <button
                    onClick={() => setCurrentDate(new Date())}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => navigateMonth('next')}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <ChevronRight className="h-5 w-5 text-gray-600" />
                  </button>
                </div>
              </div>

              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-1 mb-4">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                  <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-1">
                {calendarDays.map((day) => {
                  const dayBookings = getBookingsForDate(day);
                  const isCurrentMonth = isSameMonth(day, currentDate);
                  const isSelected = selectedDate && isSameDay(day, selectedDate);
                  const isTodayDate = isToday(day);

                  return (
                    <button
                      key={day.toString()}
                      onClick={() => setSelectedDate(day)}
                      className={`
                        relative p-2 min-h-[80px] border border-gray-100 hover:bg-gray-50 transition-colors text-left
                        ${!isCurrentMonth ? 'text-gray-300 bg-gray-50' : ''}
                        ${isSelected ? 'bg-var(--logo-green) bg-opacity-10 border-var(--logo-green)' : ''}
                        ${isTodayDate ? 'bg-blue-50 border-blue-200' : ''}
                      `}
                    >
                      <span className={`
                        text-sm font-medium
                        ${!isCurrentMonth ? 'text-gray-300' : 'text-gray-900'}
                        ${isTodayDate ? 'text-blue-600' : ''}
                      `}>
                        {format(day, 'd')}
                      </span>
                      
                      {dayBookings.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {dayBookings.slice(0, 2).map((booking, index) => (
                            <div
                              key={booking.id}
                              className={`text-xs p-1 rounded truncate ${getStatusColor(booking.status)}`}
                            >
                              {format(parseISO(booking.start_time), 'HH:mm')} - {booking.user_name}
                            </div>
                          ))}
                          {dayBookings.length > 2 && (
                            <div className="text-xs text-gray-500 p-1">
                              +{dayBookings.length - 2} more
                            </div>
                          )}
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Selected Date Details */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {selectedDate ? format(selectedDate, 'EEEE, MMMM d, yyyy') : 'Select a date'}
              </h3>

              {selectedDate ? (
                selectedDateBookings.length > 0 ? (
                  <div className="space-y-4">
                    {selectedDateBookings.map((booking) => (
                      <div key={booking.id} className="border border-gray-200 rounded-xl p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                            {booking.status.replace(/_/g, ' ').toUpperCase()}
                          </span>
                          <div className="flex gap-2">
                            <Link
                              to={`/booking/${booking.id}`}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <Eye className="h-4 w-4 text-gray-600" />
                            </Link>
                            <Link
                              to={`/modify-booking/${booking.id}`}
                              className="p-1 hover:bg-gray-100 rounded"
                            >
                              <Edit3 className="h-4 w-4 text-gray-600" />
                            </Link>
                          </div>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 text-gray-400 mr-2" />
                            <span>
                              {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                            </span>
                          </div>
                          
                          <div className="flex items-center">
                            <User className="h-4 w-4 text-gray-400 mr-2" />
                            <span>{booking.user_name}</span>
                          </div>
                          
                          <div className="flex items-center">
                            <DollarSign className="h-4 w-4 text-gray-400 mr-2" />
                            <span>${booking.total_cost}</span>
                          </div>
                          
                          {booking.location && (
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="truncate">{booking.location}</span>
                            </div>
                          )}
                        </div>

                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <p className="text-sm font-medium text-gray-900">{booking.service_type}</p>
                          {booking.special_requirements && (
                            <p className="text-xs text-gray-600 mt-1">{booking.special_requirements}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalendarIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No bookings for this date</p>
                  </div>
                )
              ) : (
                <div className="text-center py-8">
                  <CalendarIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Click on a date to view bookings</p>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="mt-6 bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Bookings</span>
                  <span className="font-semibold text-gray-900">
                    {bookings.filter(b => isSameMonth(parseISO(b.start_time), currentDate)).length}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Confirmed</span>
                  <span className="font-semibold text-blue-600">
                    {bookings.filter(b => isSameMonth(parseISO(b.start_time), currentDate) && b.status === 'confirmed').length}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Pending</span>
                  <span className="font-semibold text-yellow-600">
                    {bookings.filter(b => isSameMonth(parseISO(b.start_time), currentDate) && b.status === 'pending').length}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Revenue</span>
                  <span className="font-semibold text-var(--logo-green)">
                    ${bookings.filter(b => isSameMonth(parseISO(b.start_time), currentDate) && b.status === 'completed').reduce((sum, b) => sum + b.total_cost, 0)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/my-bookings"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              View All Bookings
            </Link>
            <Link
              to="/booking-history"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              Booking History
            </Link>
            <Link
              to="/dashboard"
              className="bg-white text-gray-700 px-6 py-4 rounded-xl font-medium hover:bg-gray-50 transition-colors border border-gray-200 text-center"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>

      {/* Availability Modal */}
      {showAvailabilityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-xl max-w-md w-full mx-4 p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Add Availability</h3>
              <button
                onClick={() => setShowAvailabilityModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <X className="h-5 w-5 text-gray-600" />
              </button>
            </div>
            
            <p className="text-gray-600 mb-6">
              Availability management feature coming soon. Currently, availability is determined by existing bookings.
            </p>
            
            <button
              onClick={() => setShowAvailabilityModal(false)}
              className="w-full bg-var(--logo-green) text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProviderBookingCalendarPage;
