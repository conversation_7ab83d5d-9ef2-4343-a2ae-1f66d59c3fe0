import React, { useState } from 'react'
import { Mail, Phone, MapPin, Clock, Send, MessageSquare, HeadphonesIcon } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    urgency: 'normal'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      // Submit contact form to database
      await dataService.submitContactForm({
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        urgency: formData.urgency,
        submitted_at: new Date().toISOString()
      })
      
      setSubmitSuccess(true)
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        urgency: 'normal'
      })
    } catch (error) {
      console.error('Error submitting contact form:', error)
      alert('Error submitting form. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }} role="main" aria-label="Contact Care Connector">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary to-primary-dark text-white">
        <div className="max-w-4xl mx-auto text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
            <MessageSquare className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 macos-title">
            Contact Us
          </h1>
          <p className="text-xl md:text-2xl opacity-90 leading-relaxed">
            We're here to help you connect with the right healthcare professionals. Reach out anytime.
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Contact Information */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-8 h-fit" style={{ boxShadow: '0 10px 30px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05)' }}>
              <h2 className="text-2xl font-bold mb-8 text-primary macos-title">
                Get in Touch
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Phone className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Phone Support</h3>
                    <p className="text-secondary text-sm mb-2">Available 24/7 for urgent matters</p>
                    <p className="font-medium text-primary">1-800-CARE-NOW</p>
                    <p className="text-sm text-secondary">(**************)</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Email Support</h3>
                    <p className="text-secondary text-sm mb-2">Response within 2 hours</p>
                    <p className="font-medium text-primary"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <HeadphonesIcon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Live Chat</h3>
                    <p className="text-secondary text-sm mb-2">Instant assistance available</p>
                    <p className="font-medium text-primary">Available in dashboard</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <MapPin className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Headquarters</h3>
                    <p className="text-secondary text-sm mb-2">Visit us during business hours</p>
                    <p className="font-medium text-primary">
                      123 Healthcare Plaza<br />
                      San Francisco, CA 94105
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Business Hours</h3>
                    <div className="text-sm text-secondary space-y-1">
                      <p><span className="font-medium">Monday - Friday:</span> 8:00 AM - 8:00 PM PST</p>
                      <p><span className="font-medium">Saturday:</span> 9:00 AM - 5:00 PM PST</p>
                      <p><span className="font-medium">Sunday:</span> 10:00 AM - 4:00 PM PST</p>
                      <p className="text-primary font-medium mt-2">Emergency support available 24/7</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h2 className="text-2xl font-bold mb-8 text-primary macos-title">
                Send Us a Message
              </h2>

              {submitSuccess ? (
                <div
                  className="text-center py-8"
                  role="alert"
                  aria-live="polite"
                  id="contact-success"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                    <Send className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-green-600 mb-2">Message Sent Successfully!</h3>
                  <p className="text-secondary mb-6">
                    Thank you for contacting us. We'll get back to you within 2 hours.
                  </p>
                  <button
                    onClick={() => setSubmitSuccess(false)}
                    className="button-primary px-6 py-3 rounded-xl font-semibold"
                  >
                    Send Another Message
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-primary mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-primary mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                        placeholder="Enter your email address"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-primary mb-2">
                        Subject *
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                        placeholder="What's this about?"
                      />
                    </div>

                    <div>
                      <label htmlFor="urgency" className="block text-sm font-medium text-primary mb-2">
                        Priority Level
                      </label>
                      <select
                        id="urgency"
                        name="urgency"
                        value={formData.urgency}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                      >
                        <option value="low">Low - General inquiry</option>
                        <option value="normal">Normal - Standard support</option>
                        <option value="high">High - Urgent matter</option>
                        <option value="emergency">Emergency - Immediate attention</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-primary mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors resize-none"
                      style={{
                        border: '1px solid var(--border-medium)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)'
                      }}
                      onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                      onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                      placeholder="Please provide details about your inquiry or how we can help you..."
                    />
                  </div>

                  <div className="bg-blue-50 p-4 rounded-xl">
                    <p className="text-sm text-blue-800">
                      <strong>For medical emergencies,</strong> please call 911 immediately. 
                      For urgent care needs, contact your healthcare provider directly or call our 24/7 support line.
                    </p>
                  </div>

                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => {
                        setFormData({
                          name: '',
                          email: '',
                          subject: '',
                          message: '',
                          urgency: 'normal'
                        })
                      }}
                      className="flex-1 py-4 rounded-xl font-semibold text-lg transition-colors"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-medium)'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                    >
                      Clear Form
                    </button>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 py-4 rounded-xl font-semibold text-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--primary-dark)')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--primary)')}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                        <span>Sending Message...</span>
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5" />
                        <span>Send Message</span>
                      </>
                    )}
                  </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary macos-title">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-secondary">
              Quick answers to common questions about our services
            </p>
          </div>
          
          <div className="space-y-6">
            <div className="rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)' }}>
              <h3 className="font-semibold text-primary mb-2">How quickly can I find a caregiver?</h3>
              <p className="text-secondary">Most families are matched with qualified caregivers within 24-48 hours of submitting their requirements.</p>
            </div>
            
            <div className="rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)' }}>
              <h3 className="font-semibold text-primary mb-2">Are all professionals background checked?</h3>
              <p className="text-secondary">Yes, every professional undergoes comprehensive background checks, verification of credentials, and reference checks before joining our platform.</p>
            </div>
            
            <div className="rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)' }}>
              <h3 className="font-semibold text-primary mb-2">What if I'm not satisfied with my caregiver?</h3>
              <p className="text-secondary">We offer a satisfaction guarantee. If you're not completely satisfied, we'll work with you to find a better match at no additional cost.</p>
            </div>
            
            <div className="rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)' }}>
              <h3 className="font-semibold text-primary mb-2">How does pricing work?</h3>
              <p className="text-secondary">Pricing varies by service type, location, and specific needs. We provide transparent pricing with no hidden fees, and you can see rates before booking.</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
